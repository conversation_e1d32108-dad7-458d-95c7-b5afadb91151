/**
 * Podcast Generator Module
 * Handles podcast script generation and browser TTS conversion
 */

class PodcastGenerator {
    constructor() {
        this.isGenerating = false;
        this.geminiAPI = null;
        this.availableVoices = [
            'zephyr', 'puck', 'charon', 'kore', 'fenrir', 'leda', 'orus', 'aoede',
            'callirrhoe', 'autonoe', 'enceladus', 'iapetus', 'umbriel', 'algieba',
            'despina', 'erinome', 'algenib', 'rasalgethi', 'laomedeia', 'achernar',
            'alnilam', 'schedar', 'gacrux', 'pulcherrima', 'achird', 'zubenelgenubi',
            'vindemiatrix', 'sadachbia', 'sadaltager', 'sulafat'
        ];

        this.initializeAPI();
    }

    initializeAPI() {
        // Get reference to the Gemini API instance
        if (window.GeminiAPI) {
            this.geminiAPI = new window.GeminiAPI();
        }
    }

    isConfigured() {
        return this.geminiAPI && this.geminiAPI.isConfigured();
    }

    async generatePodcast(script, options = {}) {
        if (this.isGenerating) {
            throw new Error('Podcast generation already in progress');
        }

        if (!this.isConfigured()) {
            throw new Error('Gemini API not configured for podcast generation');
        }

        try {
            this.isGenerating = true;
            this.updateProgress(10, 'Preparing podcast generation from transcription...');

            // Process the script (which is now transcription-based)
            const processedScript = this.processTranscriptionScript(script, options);
            console.log('Processed transcription script for TTS, length:', processedScript.length);

            this.updateProgress(30, 'Converting transcription to speech with Gemini TTS...');

            // Generate speech using Gemini TTS API with transcription data
            const audioBlob = await this.generateSpeechWithGemini(processedScript, options);

            this.updateProgress(90, 'Finalizing podcast...');

            // Create final podcast metadata
            const finalPodcast = {
                audioBlob: audioBlob,
                duration: await this.getAudioDuration(audioBlob),
                script: processedScript,
                metadata: {
                    generatedAt: new Date().toISOString(),
                    voice: options.voice || 'kore',
                    model: 'gemini-2.5-flash-preview-tts',
                    language: options.language || 'en-US',
                    source: options.useTranscription ? 'transcription' : 'generated'
                }
            };

            this.updateProgress(100, 'Podcast generation complete');
            return finalPodcast;

        } catch (error) {
            console.error('Podcast generation error:', error);
            this.updateProgress(0, 'Podcast generation failed');
            throw error;
        } finally {
            this.isGenerating = false;
        }
    }

    async initializeAudioContext() {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }

        // Create a destination for recording
        this.destination = this.audioContext.createMediaStreamDestination();
    }

    async generateSpeechWithGemini(script, options = {}) {
        try {
            console.log('Generating speech with Gemini TTS API...');

            // Ensure voice name is lowercase and valid
            let voiceName = (options.voice || 'kore').toLowerCase();
            if (!this.availableVoices.includes(voiceName)) {
                console.warn(`Voice "${voiceName}" not supported, using default "kore"`);
                voiceName = 'kore';
            }

            const languageCode = options.language || 'en-US';
            console.log(`Using voice: ${voiceName}, language: ${languageCode}`);

            const requestBody = {
                contents: [{
                    parts: [{
                        text: script
                    }]
                }],
                generationConfig: {
                    responseModalities: ["AUDIO"],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: voiceName
                            }
                        }
                    }
                }
            };

            console.log('Sending TTS request to Gemini API...');
            const response = await fetch(`${this.geminiAPI.baseUrl}/models/${this.geminiAPI.ttsModel}:generateContent?key=${this.geminiAPI.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`TTS generation failed: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            console.log('Received TTS response from Gemini API');

            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts[0]) {
                throw new Error('Invalid TTS response format from Gemini API');
            }

            const audioData = data.candidates[0].content.parts[0].inline_data?.data;
            if (!audioData) {
                throw new Error('No audio data received from Gemini TTS API');
            }

            // Convert base64 audio data to blob
            const audioBytes = atob(audioData);
            const audioArray = new Uint8Array(audioBytes.length);
            for (let i = 0; i < audioBytes.length; i++) {
                audioArray[i] = audioBytes.charCodeAt(i);
            }

            const audioBlob = new Blob([audioArray], { type: 'audio/wav' });
            console.log('Generated audio blob, size:', audioBlob.size);

            return audioBlob;

        } catch (error) {
            console.error('Gemini TTS generation error:', error);
            throw new Error(`Failed to generate speech with Gemini TTS: ${error.message}`);
        }
    }

    processTranscriptionScript(script, options = {}) {
        console.log('Processing transcription script for TTS...');

        // Clean and format the transcription-based script for better TTS output
        let processed = script
            // Remove markdown formatting
            .replace(/[#*_`]/g, '')
            // Improve speaker transitions
            .replace(/(Speaker \d+):/g, '$1 says:')
            // Add natural pauses
            .replace(/\./g, '. ')
            .replace(/,/g, ', ')
            .replace(/\?/g, '? ')
            .replace(/!/g, '! ')
            .replace(/:/g, ': ')
            .replace(/;/g, '; ')
            // Add longer pauses for speaker changes
            .replace(/(Speaker \d+ says:)/g, ' ... $1 ')
            // Add pauses for paragraph breaks
            .replace(/\n\n/g, ' ... ')
            .replace(/\n/g, ' ')
            // Clean up multiple spaces
            .replace(/\s+/g, ' ')
            .trim();

        // Ensure it doesn't exceed reasonable length for TTS
        if (processed.length > 5000) {
            console.log('Script too long, truncating...');
            processed = processed.substring(0, 4800) + '... and that concludes our summary.';
        }

        console.log('Processed transcription script, final length:', processed.length);
        return processed;
    }

    processScript(script) {
        // Legacy method - redirect to transcription processing
        return this.processTranscriptionScript(script);
    }

    async setupAudioRecording() {
        // Create a MediaRecorder to capture the TTS output
        const stream = this.destination.stream;
        
        this.mediaRecorder = new MediaRecorder(stream, {
            mimeType: this.getSupportedMimeType()
        });

        this.audioChunks = [];

        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                this.audioChunks.push(event.data);
            }
        };

        return new Promise((resolve) => {
            this.mediaRecorder.onstart = () => resolve();
            this.mediaRecorder.start();
        });
    }

    async generateSpeech(script, options = {}) {
        return new Promise((resolve, reject) => {
            try {
                // Select the best available voice
                const selectedVoice = this.selectVoice(options.voice);
                
                // Create speech synthesis utterance
                const utterance = new SpeechSynthesisUtterance(script);
                
                // Configure voice settings
                utterance.voice = selectedVoice;
                utterance.rate = options.speed || 0.9; // Slightly slower for better comprehension
                utterance.pitch = options.pitch || 1.0;
                utterance.volume = 1.0;

                // Handle speech events
                utterance.onstart = () => {
                    console.log('Speech synthesis started');
                };

                utterance.onend = () => {
                    console.log('Speech synthesis completed');
                    
                    // Stop recording and get the audio blob
                    this.mediaRecorder.stop();
                    
                    this.mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(this.audioChunks, { 
                            type: this.getSupportedMimeType() 
                        });
                        resolve(audioBlob);
                    };
                };

                utterance.onerror = (event) => {
                    console.error('Speech synthesis error:', event.error);
                    reject(new Error(`Speech synthesis failed: ${event.error}`));
                };

                // Connect speech synthesis to our audio context for recording
                // Note: This is a limitation - we can't directly capture speechSynthesis output
                // In a real implementation, you'd use a cloud TTS service
                
                // Start speech synthesis
                this.speechSynthesis.speak(utterance);

            } catch (error) {
                reject(error);
            }
        });
    }

    selectVoice(preferredVoice) {
        if (preferredVoice) {
            const voice = this.voices.find(v => 
                v.name.toLowerCase().includes(preferredVoice.toLowerCase())
            );
            if (voice) return voice;
        }

        // Return the best available voice
        if (this.preferredVoices.length > 0) {
            return this.preferredVoices[0];
        }

        // Fallback to first available voice
        return this.voices[0] || null;
    }

    async createFinalPodcast(audioBlob, options = {}) {
        // For now, return the audio blob as-is
        // In a more advanced implementation, you could:
        // - Add intro/outro music
        // - Apply audio effects
        // - Normalize audio levels
        // - Add background music
        
        return audioBlob;
    }

    async getAudioDuration(audioBlob) {
        return new Promise((resolve) => {
            const audio = new Audio();
            audio.onloadedmetadata = () => {
                resolve(audio.duration);
            };
            audio.src = URL.createObjectURL(audioBlob);
        });
    }

    getSupportedMimeType() {
        const types = [
            'audio/webm;codecs=opus',
            'audio/webm',
            'audio/mp4',
            'audio/ogg;codecs=opus'
        ];
        
        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }
        
        return 'audio/webm'; // Fallback
    }

    // Alternative method using Web Audio API for better control
    async generatePodcastWithWebAudio(script, options = {}) {
        try {
            this.updateProgress(10, 'Initializing advanced audio generation...');

            // This would be used with a cloud TTS service
            // For demonstration, we'll create a simple tone-based audio
            const audioBuffer = await this.createSyntheticAudio(script.length);
            
            this.updateProgress(50, 'Processing audio...');

            // Convert AudioBuffer to Blob
            const audioBlob = await this.audioBufferToBlob(audioBuffer);

            this.updateProgress(100, 'Audio generation complete');

            return audioBlob;

        } catch (error) {
            console.error('Web Audio generation error:', error);
            throw error;
        }
    }

    async createSyntheticAudio(textLength) {
        // Create a simple synthetic audio for demonstration
        // In a real implementation, this would call a cloud TTS service
        
        const duration = Math.max(textLength / 150 * 60, 10); // Rough estimate: 150 words per minute
        const sampleRate = 44100;
        const length = sampleRate * duration;
        
        const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);
        const channelData = audioBuffer.getChannelData(0);
        
        // Generate a simple tone pattern to represent speech
        for (let i = 0; i < length; i++) {
            const time = i / sampleRate;
            const frequency = 200 + Math.sin(time * 0.5) * 50; // Varying frequency
            const amplitude = 0.1 * Math.sin(time * 2 * Math.PI * 0.1); // Amplitude modulation
            channelData[i] = amplitude * Math.sin(2 * Math.PI * frequency * time);
        }
        
        return audioBuffer;
    }

    async audioBufferToBlob(audioBuffer) {
        return new Promise((resolve) => {
            const numberOfChannels = audioBuffer.numberOfChannels;
            const length = audioBuffer.length;
            const sampleRate = audioBuffer.sampleRate;
            
            // Create WAV file
            const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
            const view = new DataView(arrayBuffer);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * numberOfChannels * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, numberOfChannels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * numberOfChannels * 2, true);
            view.setUint16(32, numberOfChannels * 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * numberOfChannels * 2, true);
            
            // Convert audio data
            let offset = 44;
            for (let i = 0; i < length; i++) {
                for (let channel = 0; channel < numberOfChannels; channel++) {
                    const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
                    view.setInt16(offset, sample * 0x7FFF, true);
                    offset += 2;
                }
            }
            
            resolve(new Blob([arrayBuffer], { type: 'audio/wav' }));
        });
    }

    createDownloadLink(audioBlob, filename = null) {
        const url = URL.createObjectURL(audioBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename || `podcast_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
        a.textContent = 'Download Podcast';
        a.className = 'btn btn-primary';
        
        return a;
    }

    getAvailableVoices() {
        return this.availableVoices.map(voice => ({
            name: voice,
            description: this.getVoiceDescription(voice),
            quality: 'Premium',
            provider: 'Gemini TTS'
        }));
    }

    getVoiceDescription(voiceName) {
        const descriptions = {
            'zephyr': 'Bright',
            'puck': 'Upbeat',
            'charon': 'Informative',
            'kore': 'Firm',
            'fenrir': 'Excitable',
            'leda': 'Youthful',
            'orus': 'Firm',
            'aoede': 'Breezy',
            'callirrhoe': 'Easy-going',
            'autonoe': 'Bright',
            'enceladus': 'Breathy',
            'iapetus': 'Clear',
            'umbriel': 'Easy-going',
            'algieba': 'Smooth',
            'despina': 'Smooth',
            'erinome': 'Clear',
            'algenib': 'Gravelly',
            'rasalgethi': 'Informative',
            'laomedeia': 'Upbeat',
            'achernar': 'Soft',
            'alnilam': 'Firm',
            'schedar': 'Even',
            'gacrux': 'Mature',
            'pulcherrima': 'Forward',
            'achird': 'Friendly',
            'zubenelgenubi': 'Casual',
            'vindemiatrix': 'Gentle',
            'sadachbia': 'Lively',
            'sadaltager': 'Knowledgeable',
            'sulafat': 'Warm'
        };
        return descriptions[voiceName.toLowerCase()] || 'Natural';
    }

    updateProgress(percentage, message) {
        if (window.updateProgress) {
            window.updateProgress(percentage, message);
        }
    }

    cleanup() {
        // Reset generation state
        this.isGenerating = false;
    }

    stop() {
        this.cleanup();
    }
}

// Export for use in other modules
window.PodcastGenerator = PodcastGenerator;
