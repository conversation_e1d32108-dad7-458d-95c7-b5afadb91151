/**
 * Main Application Controller
 * Coordinates all modules and handles user interactions
 */

class AudioSummarizerApp {
    constructor() {
        this.audioProcessor = new AudioProcessor();
        this.geminiAPI = new GeminiAPI();
        this.speakerDetection = new SpeakerDetection();
        this.podcastGenerator = new PodcastGenerator(this.geminiAPI);
        
        this.currentRecording = null;
        this.currentTranscription = null;
        this.currentSummary = null;
        this.currentSpeakers = null;
        this.currentPodcast = null;
        
        this.recordingTimer = null;
        this.recordingStartTime = null;
        
        this.initializeApp();
    }

    initializeApp() {
        this.setupEventListeners();
        this.checkApiConfiguration();
        this.updateUI();
    }

    setupEventListeners() {
        // Settings Panel
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.toggleSettingsPanel();
        });

        document.getElementById('closeSettings').addEventListener('click', () => {
            this.hideSettingsPanel();
        });

        // API Configuration - Settings Panel
        document.getElementById('saveApiKey').addEventListener('click', () => {
            this.saveApiKey();
        });

        // API Configuration - Fallback Panel
        document.getElementById('saveApiKeyFallback').addEventListener('click', () => {
            this.saveApiKeyFallback();
        });

        // Close settings panel when clicking outside
        document.addEventListener('click', (e) => {
            const settingsPanel = document.getElementById('settingsPanel');
            const settingsBtn = document.getElementById('settingsBtn');

            if (settingsPanel.classList.contains('active') &&
                !settingsPanel.contains(e.target) &&
                !settingsBtn.contains(e.target)) {
                this.hideSettingsPanel();
            }
        });

        // Recording Controls
        document.getElementById('startRecording').addEventListener('click', () => {
            this.startRecording();
        });

        document.getElementById('stopRecording').addEventListener('click', () => {
            this.stopRecording();
        });

        document.getElementById('pauseRecording').addEventListener('click', () => {
            this.pauseRecording();
        });

        // Processing
        document.getElementById('processAudio').addEventListener('click', () => {
            this.processAudio();
        });

        // Results
        document.getElementById('exportResults').addEventListener('click', () => {
            this.exportResults();
        });

        document.getElementById('clearResults').addEventListener('click', () => {
            this.clearResults();
        });

        // Podcast Generation
        document.getElementById('generatePodcastBtn').addEventListener('click', () => {
            this.generatePodcast();
        });

        // Tab Navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window events
        window.addEventListener('beforeunload', (e) => {
            if (this.audioProcessor.isRecording) {
                e.preventDefault();
                e.returnValue = 'Recording in progress. Are you sure you want to leave?';
            }
        });
    }

    checkApiConfiguration() {
        if (!this.geminiAPI.isConfigured()) {
            this.showApiConfigPanel();
            this.showToast('Please configure your Gemini API key to use AI features', 'warning');
        }
    }

    toggleSettingsPanel() {
        const panel = document.getElementById('settingsPanel');
        panel.classList.toggle('active');

        // Sync API key between settings and fallback inputs
        const settingsInput = document.getElementById('geminiApiKey');
        const fallbackInput = document.getElementById('geminiApiKeyFallback');
        if (settingsInput.value && !fallbackInput.value) {
            fallbackInput.value = settingsInput.value;
        } else if (fallbackInput.value && !settingsInput.value) {
            settingsInput.value = fallbackInput.value;
        }
    }

    hideSettingsPanel() {
        document.getElementById('settingsPanel').classList.remove('active');
    }

    toggleApiConfigPanel() {
        const panel = document.getElementById('apiConfigPanel');
        panel.classList.toggle('active');
    }

    showApiConfigPanel() {
        document.getElementById('apiConfigPanel').classList.add('active');
    }

    hideApiConfigPanel() {
        document.getElementById('apiConfigPanel').classList.remove('active');
    }

    async saveApiKey() {
        const apiKeyInput = document.getElementById('geminiApiKey');
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            this.showToast('Please enter a valid API key', 'error');
            return;
        }

        try {
            this.showLoading('Validating API key...');

            this.geminiAPI.saveApiKey(apiKey);
            await this.geminiAPI.validateApiKey();

            // Sync with fallback input
            document.getElementById('geminiApiKeyFallback').value = apiKey;

            this.hideLoading();
            this.hideSettingsPanel();
            this.showToast('API key validated and saved successfully', 'success');
            
            // Clear the input for security
            apiKeyInput.value = '';
            
        } catch (error) {
            this.hideLoading();
            this.showToast(`API key validation failed: ${error.message}`, 'error');
        }
    }

    async saveApiKeyFallback() {
        const apiKeyInput = document.getElementById('geminiApiKeyFallback');
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            this.showToast('Please enter a valid API key', 'error');
            return;
        }

        try {
            this.showLoading('Validating API key...');

            this.geminiAPI.saveApiKey(apiKey);
            await this.geminiAPI.validateApiKey();

            // Sync with settings input
            document.getElementById('geminiApiKey').value = apiKey;

            this.hideLoading();
            this.hideApiConfigPanel();
            this.showToast('API key validated and saved successfully', 'success');

            // Clear the input for security
            apiKeyInput.value = '';

        } catch (error) {
            this.hideLoading();
            this.showToast(`API key validation failed: ${error.message}`, 'error');
        }
    }

    async startRecording() {
        try {
            const success = await this.audioProcessor.startRecording();
            
            if (success) {
                this.updateRecordingUI(true);
                this.startRecordingTimer();
                this.clearResults();
            }
            
        } catch (error) {
            this.showToast(`Failed to start recording: ${error.message}`, 'error');
        }
    }

    stopRecording() {
        console.log('Stopping recording...');
        this.audioProcessor.stopRecording();
        this.updateRecordingUI(false);
        this.stopRecordingTimer();

        // Wait a moment for the recording to be processed
        setTimeout(() => {
            this.currentRecording = this.audioProcessor.getRecordedAudio();
            console.log('Retrieved recording after stop:', !!this.currentRecording);

            if (this.currentRecording) {
                document.getElementById('processAudio').disabled = false;
                this.showToast('Recording saved. Ready for processing.', 'success');
                console.log('Recording ready, size:', this.currentRecording.size);
            } else {
                this.showToast('Recording failed to save properly', 'error');
                console.error('No recording available after stop');
            }
        }, 100); // Small delay to ensure recording is processed
    }

    pauseRecording() {
        if (this.audioProcessor.isPaused) {
            this.audioProcessor.resumeRecording();
            this.startRecordingTimer();
        } else {
            this.audioProcessor.pauseRecording();
            this.stopRecordingTimer();
        }
        
        this.updatePauseButton();
    }

    async processAudio() {
        console.log('processAudio called');

        // Get the current recording from the audio processor
        this.currentRecording = this.audioProcessor.getRecordedAudio();

        if (!this.currentRecording) {
            console.error('No recording found');
            this.showToast('No recording available to process. Please record audio first.', 'error');
            return;
        }

        console.log('Found recording, size:', this.currentRecording.size);

        if (!this.geminiAPI.isConfigured()) {
            this.showToast('Please configure your Gemini API key first', 'error');
            this.showApiConfigPanel();
            return;
        }

        try {
            this.showLoading('Processing audio with AI...');

            const speakerIdentification = document.getElementById('speakerIdentification').checked;
            const realTimeProcessing = document.getElementById('realTimeProcessing').checked;

            console.log('Starting transcription...');
            // Step 1: Transcribe audio
            this.updateProgress(10, 'Transcribing audio...');
            const transcriptionResult = await this.geminiAPI.transcribeAudio(
                this.currentRecording,
                { speakerIdentification }
            );

            console.log('Transcription completed');
            this.currentTranscription = transcriptionResult;
            this.displayTranscription(transcriptionResult);

            // Step 2: Analyze speakers if enabled
            if (speakerIdentification) {
                console.log('Starting speaker analysis...');
                this.updateProgress(40, 'Analyzing speakers...');
                try {
                    const speakerResults = await this.speakerDetection.analyzeSpeakers(
                        this.currentRecording,
                        transcriptionResult.transcription
                    );

                    console.log('Speaker analysis completed');
                    this.currentSpeakers = speakerResults;
                    this.displaySpeakers(speakerResults);
                } catch (speakerError) {
                    console.warn('Speaker analysis failed, continuing without it:', speakerError);
                    this.showToast('Speaker analysis failed, continuing with transcription only', 'warning');
                }
            }

            // Step 3: Generate summary
            console.log('Starting summarization...');
            this.updateProgress(70, 'Generating summary...');
            const summaryResult = await this.geminiAPI.summarizeText(
                transcriptionResult.transcription,
                { speakerIdentification }
            );

            console.log('Summarization completed');
            this.currentSummary = summaryResult;
            this.displaySummary(summaryResult);

            this.updateProgress(100, 'Processing complete');
            this.hideLoading();

            document.getElementById('exportResults').disabled = false;
            this.showToast('Audio processing completed successfully', 'success');

        } catch (error) {
            this.hideLoading();
            this.showToast(`Processing failed: ${error.message}`, 'error');
            console.error('Processing error:', error);
        }
    }

    async generatePodcast() {
        if (!this.currentTranscription) {
            this.showToast('Please process audio and generate a transcription first', 'error');
            return;
        }

        try {
            this.showLoading('Generating podcast...');

            // Use the transcribed text directly for podcast generation
            this.updateProgress(20, 'Creating podcast script from transcription...');

            // Generate a podcast script based on the transcription
            const podcastScript = this.createPodcastScriptFromTranscription(
                this.currentTranscription.transcription,
                this.currentSummary?.summary
            );

            // Convert transcribed text to audio using Gemini TTS
            this.updateProgress(50, 'Converting transcription to audio...');
            const podcastResult = await this.podcastGenerator.generatePodcast(
                podcastScript,
                {
                    voice: 'kore', // Use a valid Gemini TTS voice
                    language: 'en-US',
                    useTranscription: true
                }
            );

            this.currentPodcast = podcastResult;
            this.displayPodcast(podcastResult);

            this.updateProgress(100, 'Podcast generation complete');
            this.hideLoading();

            this.showToast('Podcast generated successfully', 'success');
            this.switchTab('podcast');

        } catch (error) {
            this.hideLoading();
            this.showToast(`Podcast generation failed: ${error.message}`, 'error');
            console.error('Podcast generation error:', error);
        }
    }

    displayTranscription(result) {
        const content = document.getElementById('transcriptionContent');
        content.innerHTML = `
            <div class="transcription-result">
                <div class="transcription-meta">
                    <span class="confidence">Confidence: ${(result.confidence * 100).toFixed(1)}%</span>
                    <span class="language">Language: ${result.language}</span>
                </div>
                <div class="transcription-text">
                    ${this.formatTranscriptionText(result.transcription)}
                </div>
            </div>
        `;
    }

    displaySummary(result) {
        const content = document.getElementById('summaryContent');
        content.innerHTML = `
            <div class="summary-result">
                <div class="summary-meta">
                    <span class="word-count">Original: ${result.wordCount} words</span>
                    <span class="ratio">Compression: ${(result.summaryRatio * 100).toFixed(1)}%</span>
                </div>
                <div class="summary-text">
                    ${this.formatSummaryText(result.summary)}
                </div>
                ${result.keyPoints.length > 0 ? `
                    <div class="key-points">
                        <h4>Key Points:</h4>
                        <ul>
                            ${result.keyPoints.map(point => `<li>${point}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    displaySpeakers(results) {
        const content = document.getElementById('speakersContent');
        content.innerHTML = this.speakerDetection.formatSpeakerResults();
    }

    displayPodcast(result) {
        const content = document.getElementById('podcastContent');
        const audioUrl = URL.createObjectURL(result.audioBlob);
        
        content.innerHTML = `
            <div class="podcast-result">
                <div class="podcast-meta">
                    <span class="duration">Duration: ${result.duration || 'Unknown'}</span>
                    <span class="generated-at">Generated: ${new Date(result.metadata.generatedAt).toLocaleString()}</span>
                </div>
                <div class="podcast-controls">
                    <audio id="podcastPlayer" controls style="width: 100%; margin-bottom: 1rem;">
                        <source src="${audioUrl}" type="${result.audioBlob.type}">
                        Your browser does not support the audio element.
                    </audio>
                    <div class="podcast-actions">
                        <button class="btn btn-primary" onclick="app.downloadPodcast()">
                            <i class="fas fa-download"></i>
                            Download Podcast
                        </button>
                    </div>
                </div>
                <div class="podcast-script">
                    <h4>Podcast Script:</h4>
                    <div class="script-content">
                        ${this.formatScriptText(result.script)}
                    </div>
                </div>
            </div>
        `;
    }

    formatTranscriptionText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/(Speaker \d+:)/g, '<strong class="speaker-label">$1</strong>');
    }

    formatSummaryText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    formatScriptText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/\[([^\]]+)\]/g, '<em class="stage-direction">[$1]</em>');
    }

    createPodcastScriptFromTranscription(transcription, summary = null) {
        console.log('Creating podcast script from transcription...');

        let script = '';

        // Add intro
        script += 'Welcome to this audio summary. ';

        if (summary) {
            // If we have a summary, use it as an introduction
            script += 'Here\'s a summary of the key points: ';
            script += summary + ' ';
            script += 'Now, let\'s hear the full conversation. ';
        }

        // Clean and format the transcription for better TTS
        let cleanedTranscription = transcription
            // Remove excessive line breaks
            .replace(/\n+/g, ' ')
            // Add pauses after sentences
            .replace(/\./g, '. ')
            .replace(/\?/g, '? ')
            .replace(/!/g, '! ')
            // Add pauses after speaker changes
            .replace(/(Speaker \d+:)/g, ' $1 ')
            // Clean up multiple spaces
            .replace(/\s+/g, ' ')
            .trim();

        script += cleanedTranscription;

        // Add outro
        script += ' Thank you for listening to this audio summary.';

        console.log('Generated podcast script, length:', script.length);
        return script;
    }

    downloadPodcast() {
        if (this.currentPodcast) {
            const link = this.podcastGenerator.createDownloadLink(this.currentPodcast.audioBlob);
            link.click();
        }
    }

    exportResults() {
        const results = {
            timestamp: new Date().toISOString(),
            transcription: this.currentTranscription,
            summary: this.currentSummary,
            speakers: this.currentSpeakers,
            podcast: this.currentPodcast ? {
                script: this.currentPodcast.script,
                metadata: this.currentPodcast.metadata
            } : null
        };

        const blob = new Blob([JSON.stringify(results, null, 2)], { 
            type: 'application/json' 
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `audio_summary_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.showToast('Results exported successfully', 'success');
    }

    clearResults() {
        this.currentTranscription = null;
        this.currentSummary = null;
        this.currentSpeakers = null;
        this.currentPodcast = null;
        
        document.getElementById('transcriptionContent').innerHTML = 
            '<p class="placeholder-text">Transcription will appear here after processing...</p>';
        document.getElementById('summaryContent').innerHTML = 
            '<p class="placeholder-text">Summary will appear here after processing...</p>';
        document.getElementById('speakersContent').innerHTML = 
            '<p class="placeholder-text">Speaker analysis will appear here after processing...</p>';
        document.getElementById('podcastContent').innerHTML = `
            <div class="podcast-controls">
                <button id="generatePodcastBtn" class="btn btn-primary">
                    <i class="fas fa-podcast"></i>
                    Generate Podcast
                </button>
            </div>
            <p class="placeholder-text">Generated podcast will appear here...</p>
        `;
        
        // Re-attach event listener for generate podcast button
        document.getElementById('generatePodcastBtn').addEventListener('click', () => {
            this.generatePodcast();
        });
        
        document.getElementById('exportResults').disabled = true;
        this.showToast('Results cleared', 'success');
    }

    switchTab(tabName) {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
        
        // Add active class to selected tab and panel
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}Tab`).classList.add('active');
    }

    updateRecordingUI(isRecording) {
        document.getElementById('startRecording').disabled = isRecording;
        document.getElementById('stopRecording').disabled = !isRecording;
        document.getElementById('pauseRecording').disabled = !isRecording;
        
        const status = document.getElementById('recordingStatus');
        status.textContent = isRecording ? 'Recording...' : 'Ready to record';
        status.className = isRecording ? 'status-text recording' : 'status-text';
    }

    updatePauseButton() {
        const btn = document.getElementById('pauseRecording');
        const icon = btn.querySelector('i');
        
        if (this.audioProcessor.isPaused) {
            icon.className = 'fas fa-play';
            btn.innerHTML = '<i class="fas fa-play"></i> Resume';
        } else {
            icon.className = 'fas fa-pause';
            btn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        }
    }

    startRecordingTimer() {
        this.recordingStartTime = Date.now();
        this.recordingTimer = setInterval(() => {
            this.updateRecordingTime();
        }, 1000);
    }

    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }

    updateRecordingTime() {
        if (!this.recordingStartTime) return;
        
        const elapsed = Math.floor((Date.now() - this.recordingStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        document.getElementById('recordingTime').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    handleKeyboardShortcuts(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'r':
                    e.preventDefault();
                    if (!this.audioProcessor.isRecording) {
                        this.startRecording();
                    } else {
                        this.stopRecording();
                    }
                    break;
                case 'p':
                    e.preventDefault();
                    if (this.audioProcessor.isRecording) {
                        this.pauseRecording();
                    }
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (this.currentRecording && !this.audioProcessor.isRecording) {
                        this.processAudio();
                    }
                    break;
            }
        }
    }

    updateUI() {
        // Update UI based on current state
        document.getElementById('processAudio').disabled = !this.currentRecording;
        document.getElementById('exportResults').disabled = !this.currentTranscription;
    }

    // Global utility functions
    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = document.getElementById('loadingText');
        text.textContent = message;
        overlay.classList.add('active');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
        this.updateProgress(0, '');
    }

    updateProgress(percentage, message) {
        const fill = document.getElementById('progressFill');
        const text = document.getElementById('loadingText');
        
        fill.style.width = `${percentage}%`;
        if (message) {
            text.textContent = message;
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        toast.innerHTML = `
            <i class="fas fa-${this.getToastIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        container.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
        
        // Remove on click
        toast.addEventListener('click', () => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
    }

    getToastIcon(type) {
        switch (type) {
            case 'success': return 'check-circle';
            case 'error': return 'exclamation-circle';
            case 'warning': return 'exclamation-triangle';
            default: return 'info-circle';
        }
    }

    cleanup() {
        this.audioProcessor.cleanup();
        this.speakerDetection.cleanup();
        this.podcastGenerator.cleanup();
        
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
        }
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AudioSummarizerApp();
    
    // Make utility functions globally available
    window.showToast = (message, type) => window.app.showToast(message, type);
    window.updateProgress = (percentage, message) => window.app.updateProgress(percentage, message);
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.app) {
        window.app.cleanup();
    }
});
